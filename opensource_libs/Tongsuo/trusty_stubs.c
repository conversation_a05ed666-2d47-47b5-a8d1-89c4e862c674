/*
 * Trusty environment stubs for Tongsuo
 * Provides empty implementations for system calls not available in TEE
 */

#include <stdint.h>
#include <signal.h>
#include <setjmp.h>
#include <string.h>
#include <stddef.h>

// Signal handling stubs - not available in TEE environment
int sigfillset(sigset_t *set) {
    (void)set;
    return 0;
}

int sigdelset(sigset_t *set, int signum) {
    (void)set;
    (void)signum;
    return 0;
}

int sigprocmask(int how, const sigset_t *set, sigset_t *oldset) {
    (void)how;
    (void)set;
    (void)oldset;
    return 0;
}

int sigaction(int signum, const struct sigaction *act, struct sigaction *oldact) {
    (void)signum;
    (void)act;
    (void)oldact;
    return 0;
}

// setjmp/longjmp stubs - simplified for TEE
int sigsetjmp(sigjmp_buf env, int savemask) {
    (void)env;
    (void)savemask;
    return 0;
}

// ARM capability probe functions - return 0 (feature not available)
void _armv7_neon_probe(void) {
    // Empty stub
}

void _armv8_pmull_probe(void) {
    // Empty stub
}

void _armv8_aes_probe(void) {
    // Empty stub
}

void _armv8_sha1_probe(void) {
    // Empty stub
}

void _armv8_sha256_probe(void) {
    // Empty stub
}

void _armv8_sm4_probe(void) {
    // Empty stub
}

void _armv8_sha512_probe(void) {
    // Empty stub
}

// Tongsuo-specific probe functions with symbol prefix
void TONGSUO__armv7_neon_probe(void) {
    // Empty stub
}

void TONGSUO__armv8_pmull_probe(void) {
    // Empty stub
}

void TONGSUO__armv8_aes_probe(void) {
    // Empty stub
}

void TONGSUO__armv8_sha1_probe(void) {
    // Empty stub
}

void TONGSUO__armv8_sha256_probe(void) {
    // Empty stub
}

void TONGSUO__armv8_sm4_probe(void) {
    // Empty stub
}

void TONGSUO__armv8_sha512_probe(void) {
    // Empty stub
}

void TONGSUO__armv7_tick(void) {
    // Empty stub
}

void TONGSUO__armv8_sm3_probe(void) {
    // Empty stub
}

void TONGSUO__armv8_cpuid_probe(void) {
    // Empty stub
}

// Additional system call stubs
void siglongjmp(sigjmp_buf env, int val) {
    (void)env;
    (void)val;
    // Empty stub - should not be called in TEE
}

// User/group ID stubs - return safe defaults
unsigned int getuid(void) {
    return 0;
}

unsigned int geteuid(void) {
    return 0;
}

unsigned int getgid(void) {
    return 0;
}

unsigned int getegid(void) {
    return 0;
}

// Crypto function stubs
void TONGSUO_SHA3_absorb(void *ctx, const void *inp, size_t len, size_t r) {
    (void)ctx;
    (void)inp;
    (void)len;
    (void)r;
    // Empty stub
}

void TONGSUO_SHA3_squeeze(void *ctx, void *out, size_t len, size_t r) {
    (void)ctx;
    (void)out;
    (void)len;
    (void)r;
    // Empty stub
}

int TONGSUO_CRYPTO_memcmp(const void *a, const void *b, size_t len) {
    // Use standard memcmp
    return memcmp(a, b, len);
}

// Curve448 stubs
void TONGSUO_ossl_x448_public_from_private(void *pub, const void *priv) {
    (void)pub;
    (void)priv;
    // Empty stub
}

void TONGSUO_ossl_ed448_public_from_private(void *pub, const void *priv) {
    (void)pub;
    (void)priv;
    // Empty stub
}

// ARM crypto assembly function stubs
void TONGSUO_sm4_v8_set_encrypt_key(const unsigned char *userKey, void *key) {
    (void)userKey;
    (void)key;
    // Empty stub
}

void TONGSUO_sm4_v8_set_decrypt_key(const unsigned char *userKey, void *key) {
    (void)userKey;
    (void)key;
    // Empty stub
}

void TONGSUO_sm4_v8_encrypt(const unsigned char *in, unsigned char *out, const void *key) {
    (void)in;
    (void)out;
    (void)key;
    // Empty stub
}

void TONGSUO_sm4_v8_decrypt(const unsigned char *in, unsigned char *out, const void *key) {
    (void)in;
    (void)out;
    (void)key;
    // Empty stub
}

void TONGSUO_sm4_v8_cbc_encrypt(const unsigned char *in, unsigned char *out, size_t length, const void *key, unsigned char *ivec, int enc) {
    (void)in;
    (void)out;
    (void)length;
    (void)key;
    (void)ivec;
    (void)enc;
    // Empty stub
}

int TONGSUO_aes_v8_set_decrypt_key(const unsigned char *userKey, int bits, void *key) {
    (void)userKey;
    (void)bits;
    (void)key;
    return 0;
}

void TONGSUO_aes_v8_decrypt(const unsigned char *in, unsigned char *out, const void *key) {
    (void)in;
    (void)out;
    (void)key;
    // Empty stub
}

void TONGSUO_aes_v8_cbc_encrypt(const unsigned char *in, unsigned char *out, size_t length, const void *key, unsigned char *ivec, int enc) {
    (void)in;
    (void)out;
    (void)length;
    (void)key;
    (void)ivec;
    (void)enc;
    // Empty stub
}

int TONGSUO_aes_v8_set_encrypt_key(const unsigned char *userKey, int bits, void *key) {
    (void)userKey;
    (void)bits;
    (void)key;
    return 0;
}

void TONGSUO_aes_v8_encrypt(const unsigned char *in, unsigned char *out, const void *key) {
    (void)in;
    (void)out;
    (void)key;
    // Empty stub
}

void TONGSUO_ChaCha20_ctr32(unsigned char *out, const unsigned char *inp, size_t len, const unsigned int key[8], const unsigned int counter[4]) {
    (void)out;
    (void)inp;
    (void)len;
    (void)key;
    (void)counter;
    // Empty stub
}

void TONGSUO_gcm_ghash_v8(unsigned long long Xi[2], const void *Htable, const unsigned char *inp, size_t len) {
    (void)Xi;
    (void)Htable;
    (void)inp;
    (void)len;
    // Empty stub
}

// Additional ARM GCM functions
void TONGSUO_gcm_init_v8(void *Htable, const unsigned long long H[2]) {
    (void)Htable;
    (void)H;
    // Empty stub
}

void TONGSUO_armv8_aes_gcm_encrypt(const unsigned char *in, unsigned char *out, size_t len, const void *key, unsigned char ivec[16], void *Xi) {
    (void)in;
    (void)out;
    (void)len;
    (void)key;
    (void)ivec;
    (void)Xi;
    // Empty stub
}

void TONGSUO_armv8_aes_gcm_decrypt(const unsigned char *in, unsigned char *out, size_t len, const void *key, unsigned char ivec[16], void *Xi) {
    (void)in;
    (void)out;
    (void)len;
    (void)key;
    (void)ivec;
    (void)Xi;
    // Empty stub
}

// Provider-related stubs
void* TONGSUO_ossl_pool_acquire_entropy(void *pool) {
    (void)pool;
    return NULL;
}

void* TONGSUO_ossl_prov_bio_from_dispatch(void *provctx) {
    (void)provctx;
    return NULL;
}

void* TONGSUO_ossl_prov_ctx_new(void) {
    return NULL;
}

int TONGSUO_ossl_bio_prov_init_bio_method(void) {
    return 0;
}

int TONGSUO_ossl_pool_add_nonce_data(void *pool) {
    (void)pool;
    return 0;
}

int TONGSUO_ossl_prov_seeding_from_dispatch(void *provctx) {
    (void)provctx;
    return 0;
}

void TONGSUO_ossl_prov_ctx_free(void *ctx) {
    (void)ctx;
    // Empty stub
}

void TONGSUO_ossl_prov_ctx_set0_libctx(void *ctx, void *libctx) {
    (void)ctx;
    (void)libctx;
    // Empty stub
}

void TONGSUO_ossl_prov_ctx_set0_handle(void *ctx, void *handle) {
    (void)ctx;
    (void)handle;
    // Empty stub
}

// Additional SM4 functions
void TONGSUO_sm4_v8_ecb_encrypt(const unsigned char *in, unsigned char *out, size_t length, const void *key, int enc) {
    (void)in;
    (void)out;
    (void)length;
    (void)key;
    (void)enc;
    // Empty stub
}

// Provider core functions
void TONGSUO_ossl_prov_ctx_set0_core_bio_method(void *ctx, void *method) {
    (void)ctx;
    (void)method;
    // Empty stub
}

void* TONGSUO_ossl_prov_ctx_get0_core_bio_method(void *ctx) {
    (void)ctx;
    return NULL;
}

int TONGSUO_ossl_prov_cache_exported_algorithms(void *ctx) {
    (void)ctx;
    return 0;
}

void* TONGSUO_ossl_prov_get_capabilities(void *ctx) {
    (void)ctx;
    return NULL;
}

// Cipher function tables - return NULL for all
void* TONGSUO_ossl_null_functions = NULL;
void* TONGSUO_ossl_aes256ecb_functions = NULL;
void* TONGSUO_ossl_aes192ecb_functions = NULL;
void* TONGSUO_ossl_aes128ecb_functions = NULL;
void* TONGSUO_ossl_aes256cbc_functions = NULL;
void* TONGSUO_ossl_aes192cbc_functions = NULL;
void* TONGSUO_ossl_aes128cbc_functions = NULL;

// Encoder functions
void* TONGSUO_ossl_rsa_to_text_encoder_functions = NULL;
