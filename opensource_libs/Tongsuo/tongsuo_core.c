/* Copyright (c) 2024, Tongsuo TEE Adaptation
 *
 * Permission to use, copy, modify, and/or distribute this software for any
 * purpose with or without fee is hereby granted, provided that the above
 * copyright notice and this permission notice appear in all copies.
 *
 * THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES
 * WITH REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF
 * MERCHANTABILITY AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
 * SPECIAL, DIRECT, INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES
 * WHATSOEVER RESULTING FROM LOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION
 * OF CONTRACT, NEGLIGENCE OR OTHER TORTIOUS ACTION, ARISING OUT OF OR IN
 * CONNECTION WITH THE USE OR PERFORMANCE OF THIS SOFTWARE. */

/*
 * Tongsuo Core Module for TEE Environment
 * This file includes all core cryptographic implementations in a single
 * compilation unit, similar to <PERSON>ring<PERSON><PERSON>'s bcm.c approach.
 */

#if !defined(_GNU_SOURCE)
#define _GNU_SOURCE
#endif

#include <openssl/crypto.h>
#include <openssl/opensslconf.h>

// Core algorithm implementations - organized by category

// === Symmetric Encryption ===
#ifdef TONGSUO_INCLUDE_AES
#include "crypto/aes/aes_core.c"
#ifdef OPENSSL_NO_ASM
#include "crypto/aes/aes_nohw.c"
#endif
#include "crypto/aes/aes_cbc.c"
#include "crypto/aes/aes_cfb.c"
#include "crypto/aes/aes_ctr.c"
#include "crypto/aes/aes_ecb.c"
#include "crypto/aes/aes_ige.c"
#include "crypto/aes/aes_misc.c"
#include "crypto/aes/aes_ofb.c"
#include "crypto/aes/aes_wrap.c"
#endif

#ifdef TONGSUO_INCLUDE_SM4
#include "crypto/sm4/sm4.c"
#ifdef OPENSSL_NO_ASM
#include "crypto/sm4/sm4_nohw.c"
#endif
#endif

#ifdef TONGSUO_INCLUDE_DES
#include "crypto/des/des_enc.c"
#include "crypto/des/fcrypt_b.c"
#include "crypto/des/set_key.c"
#endif

// === Hash Functions ===
#ifdef TONGSUO_INCLUDE_SHA
#include "crypto/sha/sha1dgst.c"
#include "crypto/sha/sha1_one.c"
#include "crypto/sha/sha256.c"
#include "crypto/sha/sha3.c"
#include "crypto/sha/sha512.c"
#ifdef OPENSSL_NO_ASM
#include "crypto/sha/sha_nohw.c"
#endif
#endif

#ifdef TONGSUO_INCLUDE_SM3
#include "crypto/sm3/sm3.c"
#ifdef OPENSSL_NO_ASM
#include "crypto/sm3/sm3_nohw.c"
#endif
#endif

#ifdef TONGSUO_INCLUDE_MD5
#include "crypto/md5/md5_dgst.c"
#include "crypto/md5/md5_one.c"
#endif

// === MAC Functions ===
#ifdef TONGSUO_INCLUDE_HMAC
#include "crypto/hmac/hmac.c"
#endif

#ifdef TONGSUO_INCLUDE_CMAC
#include "crypto/cmac/cmac.c"
#endif

// === Modes of Operation ===
#ifdef TONGSUO_INCLUDE_MODES
#include "crypto/modes/cbc128.c"
#include "crypto/modes/ccm128.c"
#include "crypto/modes/cfb128.c"
#include "crypto/modes/ctr128.c"
#include "crypto/modes/cts128.c"
#include "crypto/modes/gcm128.c"
#ifdef OPENSSL_NO_ASM
#include "crypto/modes/gcm_nohw.c"
#endif
#include "crypto/modes/ocb128.c"
#include "crypto/modes/ofb128.c"
#include "crypto/modes/wrap128.c"
#include "crypto/modes/xts128.c"
#endif

// === Big Number Arithmetic ===
#ifdef TONGSUO_INCLUDE_BN
#include "crypto/bn/bn_add.c"
#include "crypto/bn/bn_blind.c"
#include "crypto/bn/bn_const.c"
#include "crypto/bn/bn_conv.c"
#include "crypto/bn/bn_ctx.c"
#include "crypto/bn/bn_div.c"
#include "crypto/bn/bn_err.c"
#include "crypto/bn/bn_exp.c"
#include "crypto/bn/bn_exp2.c"
#include "crypto/bn/bn_gcd.c"
#include "crypto/bn/bn_gf2m.c"
#include "crypto/bn/bn_intern.c"
#include "crypto/bn/bn_kron.c"
#include "crypto/bn/bn_lib.c"
#include "crypto/bn/bn_mod.c"
#include "crypto/bn/bn_mont.c"
#include "crypto/bn/bn_mpi.c"
#include "crypto/bn/bn_mul.c"
#include "crypto/bn/bn_nist.c"
#include "crypto/bn/bn_prime.c"
#include "crypto/bn/bn_print.c"
#include "crypto/bn/bn_rand.c"
#include "crypto/bn/bn_recp.c"
#include "crypto/bn/bn_shift.c"
#include "crypto/bn/bn_sqr.c"
#include "crypto/bn/bn_sqrt.c"
#include "crypto/bn/bn_word.c"
#include "crypto/bn/bn_x931p.c"
#ifdef OPENSSL_NO_ASM
#include "crypto/bn/bn_nohw.c"
#endif
#endif

// === Asymmetric Cryptography ===
#ifdef TONGSUO_INCLUDE_RSA
#include "crypto/rsa/rsa_ameth.c"
#include "crypto/rsa/rsa_asn1.c"
#include "crypto/rsa/rsa_chk.c"
#include "crypto/rsa/rsa_crpt.c"
#include "crypto/rsa/rsa_depr.c"
#include "crypto/rsa/rsa_err.c"
#include "crypto/rsa/rsa_gen.c"
#include "crypto/rsa/rsa_lib.c"
#include "crypto/rsa/rsa_none.c"
#include "crypto/rsa/rsa_oaep.c"
#include "crypto/rsa/rsa_ossl.c"
#include "crypto/rsa/rsa_pk1.c"
#include "crypto/rsa/rsa_pmeth.c"
#include "crypto/rsa/rsa_prn.c"
#include "crypto/rsa/rsa_pss.c"
#include "crypto/rsa/rsa_saos.c"
#include "crypto/rsa/rsa_sign.c"
#include "crypto/rsa/rsa_sp800_56b_check.c"
#include "crypto/rsa/rsa_sp800_56b_gen.c"
#include "crypto/rsa/rsa_ssl.c"
#include "crypto/rsa/rsa_x931.c"
#include "crypto/rsa/rsa_x931g.c"
#endif

#ifdef TONGSUO_INCLUDE_EC
#include "crypto/ec/ec_lib.c"
#include "crypto/ec/ecp_smpl.c"
#include "crypto/ec/ec_mult.c"
#include "crypto/ec/ec_err.c"
#include "crypto/ec/ec_curve.c"
#include "crypto/ec/ec_check.c"
#include "crypto/ec/ec_print.c"
#include "crypto/ec/ec_asn1.c"
#include "crypto/ec/ec_key.c"
#include "crypto/ec/ec2_smpl.c"
#include "crypto/ec/ec_cvt.c"
#include "crypto/ec/ec_oct.c"
#ifdef OPENSSL_NO_ASM
#include "crypto/ec/ec_nohw.c"
#endif
#endif

#ifdef TONGSUO_INCLUDE_SM2
#include "crypto/sm2/sm2_crypt.c"
#include "crypto/sm2/sm2_err.c"
#include "crypto/sm2/sm2_key.c"
#include "crypto/sm2/sm2_sign.c"
#endif

// === Random Number Generation ===
#ifdef TONGSUO_INCLUDE_RAND
#include "crypto/rand/randfile.c"
#include "crypto/rand/rand_lib.c"
#include "crypto/rand/rand_err.c"
#ifdef __TRUSTY__
#include "crypto/rand/rand_trusty.c"
#else
#include "crypto/rand/rand_unix.c"
#endif
#endif

// === Error Handling ===
#ifdef TONGSUO_INCLUDE_ERR
#include "crypto/err/err.c"
#include "crypto/err/err_all.c"
#include "crypto/err/err_prn.c"
#endif

// === Memory Management ===
#ifdef TONGSUO_INCLUDE_MEM
#include "crypto/mem.c"
#include "crypto/mem_dbg.c"
#ifdef __TRUSTY__
#include "crypto/mem_trusty.c"
#endif
#endif

// === ASN.1 Support ===
#ifdef TONGSUO_INCLUDE_ASN1
#include "crypto/asn1/a_bitstr.c"
#include "crypto/asn1/a_bool.c"
#include "crypto/asn1/a_d2i_fp.c"
#include "crypto/asn1/a_digest.c"
#include "crypto/asn1/a_dup.c"
#include "crypto/asn1/a_gentm.c"
#include "crypto/asn1/a_i2d_fp.c"
#include "crypto/asn1/a_int.c"
#include "crypto/asn1/a_mbstr.c"
#include "crypto/asn1/a_object.c"
#include "crypto/asn1/a_octet.c"
#include "crypto/asn1/a_print.c"
#include "crypto/asn1/a_sign.c"
#include "crypto/asn1/a_strex.c"
#include "crypto/asn1/a_strnid.c"
#include "crypto/asn1/a_time.c"
#include "crypto/asn1/a_type.c"
#include "crypto/asn1/a_utctm.c"
#include "crypto/asn1/a_utf8.c"
#include "crypto/asn1/a_verify.c"
#include "crypto/asn1/ameth_lib.c"
#include "crypto/asn1/asn1_err.c"
#include "crypto/asn1/asn1_gen.c"
#include "crypto/asn1/asn1_item_list.c"
#include "crypto/asn1/asn1_lib.c"
#include "crypto/asn1/asn1_par.c"
#include "crypto/asn1/asn_mime.c"
#include "crypto/asn1/asn_moid.c"
#include "crypto/asn1/asn_mstbl.c"
#include "crypto/asn1/asn_pack.c"
#include "crypto/asn1/bio_asn1.c"
#include "crypto/asn1/bio_ndef.c"
#include "crypto/asn1/d2i_pr.c"
#include "crypto/asn1/d2i_pu.c"
#include "crypto/asn1/evp_asn1.c"
#include "crypto/asn1/f_int.c"
#include "crypto/asn1/f_string.c"
#include "crypto/asn1/i2d_pr.c"
#include "crypto/asn1/i2d_pu.c"
#include "crypto/asn1/n_pkey.c"
#include "crypto/asn1/nsseq.c"
#include "crypto/asn1/p5_pbe.c"
#include "crypto/asn1/p5_pbev2.c"
#include "crypto/asn1/p5_scrypt.c"
#include "crypto/asn1/p8_pkey.c"
#include "crypto/asn1/t_bitst.c"
#include "crypto/asn1/t_pkey.c"
#include "crypto/asn1/t_spki.c"
#include "crypto/asn1/tasn_dec.c"
#include "crypto/asn1/tasn_enc.c"
#include "crypto/asn1/tasn_fre.c"
#include "crypto/asn1/tasn_new.c"
#include "crypto/asn1/tasn_prn.c"
#include "crypto/asn1/tasn_scn.c"
#include "crypto/asn1/tasn_typ.c"
#include "crypto/asn1/tasn_utl.c"
#include "crypto/asn1/x_algor.c"
#include "crypto/asn1/x_bignum.c"
#include "crypto/asn1/x_info.c"
#include "crypto/asn1/x_int64.c"
#include "crypto/asn1/x_long.c"
#include "crypto/asn1/x_pkey.c"
#include "crypto/asn1/x_pubkey.c"
#include "crypto/asn1/x_sig.c"
#include "crypto/asn1/x_spki.c"
#include "crypto/asn1/x_val.c"
#endif
